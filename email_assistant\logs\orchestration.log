[2025-07-09 23:36:18] [INFO] Orchestration script started with parameters: {"Start":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:36:18] [INFO] Starting all services in dependency order...
[2025-07-09 23:36:18] [INFO] Starting Qdrant Vector Database...
[2025-07-09 23:36:20] [WARN] Health check failed for Qdrant Vector Database: The operation has timed out.
[2025-07-09 23:36:20] [SUCCESS] Qdrant Vector Database started with PID 11980
[2025-07-09 23:36:20] [INFO] Waiting for Qdrant Vector Database to become healthy...
[2025-07-09 23:36:22] [WARN] Health check failed for Qdrant Vector Database: The underlying connection was closed: An unexpected error occurred on a receive.
[2025-07-09 23:36:24] [INFO] Waiting for Qdrant Vector Database... (2/10 seconds)
[2025-07-09 23:36:24] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:26] [INFO] Waiting for Qdrant Vector Database... (4/10 seconds)
[2025-07-09 23:36:26] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:28] [INFO] Waiting for Qdrant Vector Database... (6/10 seconds)
[2025-07-09 23:36:28] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:30] [INFO] Waiting for Qdrant Vector Database... (8/10 seconds)
[2025-07-09 23:36:30] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:32] [INFO] Waiting for Qdrant Vector Database... (10/10 seconds)
[2025-07-09 23:36:32] [ERROR] Qdrant Vector Database failed to become healthy within 10 seconds
[2025-07-09 23:36:32] [ERROR] Failed to start Qdrant Vector Database - health check failed
[2025-07-09 23:36:32] [ERROR] Critical service Qdrant Vector Database failed to start, aborting
[2025-07-09 23:36:32] [ERROR] Service startup failed
[2025-07-09 23:36:32] [INFO] Orchestration script completed
[2025-07-09 23:37:51] [INFO] Orchestration script started with parameters: {"Status":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:37:51] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:37:54] [WARN] Health check failed for Ingestion Service (Rust): The operation has timed out.
[2025-07-09 23:37:59] [INFO] Orchestration script completed
[2025-07-09 23:41:55] [INFO] Orchestration script started with parameters: {"Stop":{"IsPresent":true}}
[2025-07-09 23:41:55] [INFO] Stopping all services...
[2025-07-09 23:41:55] [INFO] Stopping Ingestion Service (Rust)...
[2025-07-09 23:41:57] [INFO] Ingestion Service (Rust) is not running
[2025-07-09 23:41:58] [INFO] Stopping RAG Service (Python)...
[2025-07-09 23:42:08] [WARN] Failed to stop RAG Service (Python) - port still in use
[2025-07-09 23:42:09] [INFO] Stopping Ollama LLM Service...
[2025-07-09 23:42:20] [WARN] Failed to stop Ollama LLM Service - port still in use
[2025-07-09 23:42:21] [INFO] Stopping Qdrant Vector Database...
[2025-07-09 23:42:31] [WARN] Failed to stop Qdrant Vector Database - port still in use
[2025-07-09 23:42:32] [SUCCESS] All services stopped
[2025-07-09 23:42:32] [INFO] Orchestration script completed
[2025-07-09 23:43:39] [INFO] Orchestration script started with parameters: {"Start":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:43:39] [INFO] Starting all services in dependency order...
[2025-07-09 23:43:39] [INFO] Starting Qdrant Vector Database...
[2025-07-09 23:43:41] [WARN] Health check failed for Qdrant Vector Database: The operation has timed out.
[2025-07-09 23:43:41] [SUCCESS] Qdrant Vector Database started with PID 10672
[2025-07-09 23:43:41] [INFO] Waiting for Qdrant Vector Database to become healthy...
[2025-07-09 23:43:43] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:45] [INFO] Waiting for Qdrant Vector Database... (2/10 seconds)
[2025-07-09 23:43:45] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:47] [INFO] Waiting for Qdrant Vector Database... (4/10 seconds)
[2025-07-09 23:43:47] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:49] [INFO] Waiting for Qdrant Vector Database... (6/10 seconds)
[2025-07-09 23:43:49] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:51] [INFO] Waiting for Qdrant Vector Database... (8/10 seconds)
[2025-07-09 23:43:51] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:53] [INFO] Waiting for Qdrant Vector Database... (10/10 seconds)
[2025-07-09 23:43:53] [ERROR] Qdrant Vector Database failed to become healthy within 10 seconds
[2025-07-09 23:43:53] [ERROR] Failed to start Qdrant Vector Database - health check failed
[2025-07-09 23:43:53] [ERROR] Critical service Qdrant Vector Database failed to start, aborting
[2025-07-09 23:43:53] [ERROR] Service startup failed
[2025-07-09 23:43:53] [INFO] Orchestration script completed
