[2025-07-09 23:36:18] [INFO] Orchestration script started with parameters: {"Start":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:36:18] [INFO] Starting all services in dependency order...
[2025-07-09 23:36:18] [INFO] Starting Qdrant Vector Database...
[2025-07-09 23:36:20] [WARN] Health check failed for Qdrant Vector Database: The operation has timed out.
[2025-07-09 23:36:20] [SUCCESS] Qdrant Vector Database started with PID 11980
[2025-07-09 23:36:20] [INFO] Waiting for Qdrant Vector Database to become healthy...
[2025-07-09 23:36:22] [WARN] Health check failed for Qdrant Vector Database: The underlying connection was closed: An unexpected error occurred on a receive.
[2025-07-09 23:36:24] [INFO] Waiting for Qdrant Vector Database... (2/10 seconds)
[2025-07-09 23:36:24] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:26] [INFO] Waiting for Qdrant Vector Database... (4/10 seconds)
[2025-07-09 23:36:26] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:28] [INFO] Waiting for Qdrant Vector Database... (6/10 seconds)
[2025-07-09 23:36:28] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:30] [INFO] Waiting for Qdrant Vector Database... (8/10 seconds)
[2025-07-09 23:36:30] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:36:32] [INFO] Waiting for Qdrant Vector Database... (10/10 seconds)
[2025-07-09 23:36:32] [ERROR] Qdrant Vector Database failed to become healthy within 10 seconds
[2025-07-09 23:36:32] [ERROR] Failed to start Qdrant Vector Database - health check failed
[2025-07-09 23:36:32] [ERROR] Critical service Qdrant Vector Database failed to start, aborting
[2025-07-09 23:36:32] [ERROR] Service startup failed
[2025-07-09 23:36:32] [INFO] Orchestration script completed
[2025-07-09 23:37:51] [INFO] Orchestration script started with parameters: {"Status":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:37:51] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:37:54] [WARN] Health check failed for Ingestion Service (Rust): The operation has timed out.
[2025-07-09 23:37:59] [INFO] Orchestration script completed
[2025-07-09 23:41:55] [INFO] Orchestration script started with parameters: {"Stop":{"IsPresent":true}}
[2025-07-09 23:41:55] [INFO] Stopping all services...
[2025-07-09 23:41:55] [INFO] Stopping Ingestion Service (Rust)...
[2025-07-09 23:41:57] [INFO] Ingestion Service (Rust) is not running
[2025-07-09 23:41:58] [INFO] Stopping RAG Service (Python)...
[2025-07-09 23:42:08] [WARN] Failed to stop RAG Service (Python) - port still in use
[2025-07-09 23:42:09] [INFO] Stopping Ollama LLM Service...
[2025-07-09 23:42:20] [WARN] Failed to stop Ollama LLM Service - port still in use
[2025-07-09 23:42:21] [INFO] Stopping Qdrant Vector Database...
[2025-07-09 23:42:31] [WARN] Failed to stop Qdrant Vector Database - port still in use
[2025-07-09 23:42:32] [SUCCESS] All services stopped
[2025-07-09 23:42:32] [INFO] Orchestration script completed
[2025-07-09 23:43:39] [INFO] Orchestration script started with parameters: {"Start":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:43:39] [INFO] Starting all services in dependency order...
[2025-07-09 23:43:39] [INFO] Starting Qdrant Vector Database...
[2025-07-09 23:43:41] [WARN] Health check failed for Qdrant Vector Database: The operation has timed out.
[2025-07-09 23:43:41] [SUCCESS] Qdrant Vector Database started with PID 10672
[2025-07-09 23:43:41] [INFO] Waiting for Qdrant Vector Database to become healthy...
[2025-07-09 23:43:43] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:45] [INFO] Waiting for Qdrant Vector Database... (2/10 seconds)
[2025-07-09 23:43:45] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:47] [INFO] Waiting for Qdrant Vector Database... (4/10 seconds)
[2025-07-09 23:43:47] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:49] [INFO] Waiting for Qdrant Vector Database... (6/10 seconds)
[2025-07-09 23:43:49] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:51] [INFO] Waiting for Qdrant Vector Database... (8/10 seconds)
[2025-07-09 23:43:51] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:43:53] [INFO] Waiting for Qdrant Vector Database... (10/10 seconds)
[2025-07-09 23:43:53] [ERROR] Qdrant Vector Database failed to become healthy within 10 seconds
[2025-07-09 23:43:53] [ERROR] Failed to start Qdrant Vector Database - health check failed
[2025-07-09 23:43:53] [ERROR] Critical service Qdrant Vector Database failed to start, aborting
[2025-07-09 23:43:53] [ERROR] Service startup failed
[2025-07-09 23:43:53] [INFO] Orchestration script completed
[2025-07-09 23:48:48] [INFO] Orchestration script started with parameters: {"Start":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:48:48] [INFO] Starting all services in dependency order...
[2025-07-09 23:48:48] [INFO] Starting Qdrant Vector Database...
[2025-07-09 23:48:50] [WARN] Health check failed for Qdrant Vector Database: The operation has timed out.
[2025-07-09 23:48:50] [SUCCESS] Qdrant Vector Database started with PID 17284
[2025-07-09 23:48:50] [INFO] Waiting for Qdrant Vector Database to become healthy...
[2025-07-09 23:48:52] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:48:54] [INFO] Waiting for Qdrant Vector Database... (2/10 seconds)
[2025-07-09 23:48:54] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:48:56] [INFO] Waiting for Qdrant Vector Database... (4/10 seconds)
[2025-07-09 23:48:56] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:48:58] [INFO] Waiting for Qdrant Vector Database... (6/10 seconds)
[2025-07-09 23:48:58] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:49:00] [INFO] Waiting for Qdrant Vector Database... (8/10 seconds)
[2025-07-09 23:49:00] [WARN] Health check failed for Qdrant Vector Database: The server committed a protocol violation. Section=ResponseStatusLine
[2025-07-09 23:49:02] [INFO] Waiting for Qdrant Vector Database... (10/10 seconds)
[2025-07-09 23:49:02] [ERROR] Qdrant Vector Database failed to become healthy within 10 seconds
[2025-07-09 23:49:02] [ERROR] Failed to start Qdrant Vector Database - health check failed
[2025-07-09 23:49:02] [ERROR] Critical service Qdrant Vector Database failed to start, aborting
[2025-07-09 23:49:02] [ERROR] Service startup failed
[2025-07-09 23:49:02] [INFO] Orchestration script completed
[2025-07-09 23:49:30] [INFO] Orchestration script started with parameters: {"Start":{"IsPresent":true},"Verbose":{"IsPresent":true}}
[2025-07-09 23:49:30] [INFO] Starting all services in dependency order...
[2025-07-09 23:49:30] [INFO] Starting Qdrant Vector Database...
[2025-07-09 23:49:32] [WARN] Health check failed for Qdrant Vector Database: The operation has timed out.
[2025-07-09 23:49:33] [SUCCESS] Qdrant Vector Database started with PID 44272
[2025-07-09 23:49:33] [INFO] Waiting for Qdrant Vector Database to become healthy...
[2025-07-09 23:49:35] [SUCCESS] Qdrant Vector Database is healthy
[2025-07-09 23:49:37] [INFO] Starting Ollama LLM Service...
[2025-07-09 23:49:39] [WARN] Health check failed for Ollama LLM Service: The operation has timed out.
[2025-07-09 23:49:39] [SUCCESS] Ollama LLM Service started with PID 14720
[2025-07-09 23:49:39] [INFO] Waiting for Ollama LLM Service to become healthy...
[2025-07-09 23:49:41] [SUCCESS] Ollama LLM Service is healthy
[2025-07-09 23:49:43] [INFO] Starting RAG Service (Python)...
[2025-07-09 23:49:45] [WARN] Health check failed for RAG Service (Python): The operation has timed out.
[2025-07-09 23:49:45] [SUCCESS] RAG Service (Python) started with PID 38932
[2025-07-09 23:49:45] [INFO] Waiting for RAG Service (Python) to become healthy...
[2025-07-09 23:49:49] [WARN] Health check failed for RAG Service (Python): Unable to connect to the remote server
[2025-07-09 23:49:51] [INFO] Waiting for RAG Service (Python)... (2/20 seconds)
[2025-07-09 23:49:55] [WARN] Health check failed for RAG Service (Python): Unable to connect to the remote server
[2025-07-09 23:49:57] [INFO] Waiting for RAG Service (Python)... (4/20 seconds)
[2025-07-09 23:50:01] [WARN] Health check failed for RAG Service (Python): Unable to connect to the remote server
[2025-07-09 23:50:03] [INFO] Waiting for RAG Service (Python)... (6/20 seconds)
[2025-07-09 23:50:07] [WARN] Health check failed for RAG Service (Python): Unable to connect to the remote server
[2025-07-09 23:50:09] [INFO] Waiting for RAG Service (Python)... (8/20 seconds)
[2025-07-09 23:50:13] [WARN] Health check failed for RAG Service (Python): Unable to connect to the remote server
[2025-07-09 23:50:15] [INFO] Waiting for RAG Service (Python)... (10/20 seconds)
[2025-07-09 23:50:19] [SUCCESS] RAG Service (Python) is healthy
[2025-07-09 23:50:21] [INFO] Starting Ingestion Service (Rust)...
[2025-07-09 23:50:23] [WARN] Health check failed for Ingestion Service (Rust): The operation has timed out.
[2025-07-09 23:50:23] [SUCCESS] Ingestion Service (Rust) started with PID 19628
[2025-07-09 23:50:23] [INFO] Waiting for Ingestion Service (Rust) to become healthy...
[2025-07-09 23:50:27] [WARN] Health check failed for Ingestion Service (Rust): Unable to connect to the remote server
[2025-07-09 23:50:29] [INFO] Waiting for Ingestion Service (Rust)... (2/15 seconds)
[2025-07-09 23:50:33] [WARN] Health check failed for Ingestion Service (Rust): Unable to connect to the remote server
[2025-07-09 23:50:35] [INFO] Waiting for Ingestion Service (Rust)... (4/15 seconds)
[2025-07-09 23:50:39] [WARN] Health check failed for Ingestion Service (Rust): Unable to connect to the remote server
[2025-07-09 23:50:41] [INFO] Waiting for Ingestion Service (Rust)... (6/15 seconds)
[2025-07-09 23:50:45] [WARN] Health check failed for Ingestion Service (Rust): Unable to connect to the remote server
[2025-07-09 23:50:47] [INFO] Waiting for Ingestion Service (Rust)... (8/15 seconds)
[2025-07-09 23:50:51] [WARN] Health check failed for Ingestion Service (Rust): Unable to connect to the remote server
[2025-07-09 23:50:53] [INFO] Waiting for Ingestion Service (Rust)... (10/15 seconds)
[2025-07-09 23:51:40] [INFO] Orchestration script started with parameters: {"Status":{"IsPresent":true}}
[2025-07-09 23:51:51] [INFO] Orchestration script completed
[2025-07-09 23:52:00] [INFO] Orchestration script started with parameters: {"Service":"ingestion_service","Start":{"IsPresent":true}}
[2025-07-09 23:52:00] [INFO] Starting Ingestion Service (Rust)...
[2025-07-09 23:52:02] [SUCCESS] Ingestion Service (Rust) started with PID 12452
[2025-07-09 23:52:02] [INFO] Waiting for Ingestion Service (Rust) to become healthy...
[2025-07-09 23:52:45] [INFO] Orchestration script started with parameters: {"Service":"ingestion_service","Start":{"IsPresent":true}}
[2025-07-09 23:52:45] [INFO] Starting Ingestion Service (Rust)...
[2025-07-09 23:52:47] [SUCCESS] Ingestion Service (Rust) started with PID 30728
[2025-07-09 23:52:47] [INFO] Waiting for Ingestion Service (Rust) to become healthy...
[2025-07-09 23:53:07] [SUCCESS] Ingestion Service (Rust) is healthy
[2025-07-09 23:53:07] [INFO] Orchestration script completed
[2025-07-09 23:53:16] [INFO] Orchestration script started with parameters: {"Status":{"IsPresent":true}}
[2025-07-09 23:53:16] [INFO] Orchestration script completed
[2025-07-09 23:59:02] [INFO] Orchestration script started with parameters: {"Service":"ingestion_service","Restart":{"IsPresent":true}}
[2025-07-09 23:59:02] [INFO] Stopping Ingestion Service (Rust)...
[2025-07-09 23:59:12] [WARN] Failed to stop Ingestion Service (Rust) - port still in use
[2025-07-09 23:59:15] [INFO] Starting Ingestion Service (Rust)...
[2025-07-09 23:59:17] [SUCCESS] Ingestion Service (Rust) started with PID 31568
[2025-07-09 23:59:17] [INFO] Waiting for Ingestion Service (Rust) to become healthy...
