{
    "ServiceName":  "qdrant",
    "ProcessId":  44272,
    "StartTime":  {
                      "value":  "\/Date(1752094172999)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 9, 2025 11:49:32 PM"
                  }
}
{
    "ServiceName":  "ollama",
    "ProcessId":  14720,
    "StartTime":  {
                      "value":  "\/Date(1752094179068)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 9, 2025 11:49:39 PM"
                  }
}
{
    "ServiceName":  "rag_service",
    "ProcessId":  38932,
    "StartTime":  {
                      "value":  "\/Date(1752094185191)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 9, 2025 11:49:45 PM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  19628,
    "StartTime":  {
                      "value":  "\/Date(1752094223394)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 9, 2025 11:50:23 PM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  12452,
    "StartTime":  {
                      "value":  "\/Date(1752094322246)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 9, 2025 11:52:02 PM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  30728,
    "StartTime":  {
                      "value":  "\/Date(1752094367296)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 9, 2025 11:52:47 PM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  31568,
    "StartTime":  {
                      "value":  "\/Date(1752094757121)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 9, 2025 11:59:17 PM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  34136,
    "StartTime":  {
                      "value":  "\/Date(1752094818102)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:00:18 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  38344,
    "StartTime":  {
                      "value":  "\/Date(1752094918765)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:01:58 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  24880,
    "StartTime":  {
                      "value":  "\/Date(1752095222215)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:07:02 AM"
                  }
}
{
    "ServiceName":  "qdrant",
    "ProcessId":  38668,
    "StartTime":  {
                      "value":  "\/Date(1752095401830)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:10:01 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  19620,
    "StartTime":  {
                      "value":  "\/Date(1752095415419)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:10:15 AM"
                  }
}
{
    "ServiceName":  "rag_service",
    "ProcessId":  2332,
    "StartTime":  {
                      "value":  "\/Date(1752095628004)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:13:48 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  41020,
    "StartTime":  {
                      "value":  "\/Date(1752095927200)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:18:47 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  3928,
    "StartTime":  {
                      "value":  "\/Date(1752096061501)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:21:01 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  26108,
    "StartTime":  {
                      "value":  "\/Date(1752096218285)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:23:38 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  18300,
    "StartTime":  {
                      "value":  "\/Date(1752096369252)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:26:09 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  10372,
    "StartTime":  {
                      "value":  "\/Date(1752097566958)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:46:06 AM"
                  }
}
{
    "ServiceName":  "ingestion_service",
    "ProcessId":  16104,
    "StartTime":  {
                      "value":  "\/Date(1752097950247)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 10, 2025 12:52:30 AM"
                  }
}
