// AI-Assisted Email Response System - Ingestion Service HTTP Server
// Provides REST API endpoints for the desktop application

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
    Json,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;
use tracing::{info, warn, error};
use uuid::Uuid;

use ingestion_service::*;

// Application state
#[derive(Clone)]
pub struct AppState {
    pub qdrant_client: Arc<qdrant_client::Qdrant>,
    pub embedding_service_url: String,
}

// API Request/Response types
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub message: String,
    pub services: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingRequest {
    pub file_path: String,
    pub file_type: String, // "eml" or "mbox"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailProcessingResponse {
    pub success: bool,
    pub message: String,
    pub processed_count: Option<u32>,
    pub processed_ids: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailListResponse {
    pub emails: Vec<EmailSummary>,
    pub total_count: u64,
    pub page: u64,
    pub page_size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailSummary {
    pub id: String,
    pub subject: Option<String>,
    pub from_address: Option<String>,
    pub to_addresses: Vec<String>,
    pub sent_date: Option<String>,
    pub created_at: String,
    pub preview: Option<String>, // First 200 characters of content
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailDetailResponse {
    pub email: Message,
}

#[derive(Debug, Deserialize)]
pub struct ListEmailsQuery {
    pub page: Option<u64>,
    pub page_size: Option<u64>,
}

#[derive(Debug, Deserialize)]
pub struct SearchEmailsQuery {
    pub query: String,
    pub limit: Option<u64>,
    pub score_threshold: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchEmailsResponse {
    pub results: Vec<SearchResultSummary>,
    pub query: String,
    pub total_found: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResultSummary {
    pub email: EmailSummary,
    pub score: f32,
}

// Health check endpoint
async fn health_check(State(state): State<AppState>) -> Result<Json<HealthResponse>, StatusCode> {
    let mut services = HashMap::new();
    
    // Check Qdrant connection
    match state.qdrant_client.health_check().await {
        Ok(_) => {
            services.insert("qdrant".to_string(), "healthy".to_string());
        }
        Err(_) => {
            services.insert("qdrant".to_string(), "unhealthy".to_string());
        }
    }
    
    // Check embedding service
    let client = reqwest::Client::new();
    match client.get(&format!("{}/health", state.embedding_service_url)).send().await {
        Ok(response) if response.status().is_success() => {
            services.insert("embedding_service".to_string(), "healthy".to_string());
        }
        _ => {
            services.insert("embedding_service".to_string(), "unhealthy".to_string());
        }
    }
    
    let all_healthy = services.values().all(|status| status == "healthy");
    
    Ok(Json(HealthResponse {
        status: if all_healthy { "healthy".to_string() } else { "degraded".to_string() },
        message: "Ingestion service is running".to_string(),
        services,
    }))
}

// Process email files endpoint
async fn process_emails(
    State(state): State<AppState>,
    Json(request): Json<EmailProcessingRequest>,
) -> Result<Json<EmailProcessingResponse>, StatusCode> {
    info!("Processing email file: {} (type: {})", request.file_path, request.file_type);
    
    match request.file_type.as_str() {
        "eml" => {
            match ingest_email_file_with_embeddings(
                &*state.qdrant_client,
                &request.file_path,
                &state.embedding_service_url,
            ).await {
                Ok(message_id) => {
                    info!("Successfully processed EML file: {}", message_id);
                    Ok(Json(EmailProcessingResponse {
                        success: true,
                        message: "Email processed successfully".to_string(),
                        processed_count: Some(1),
                        processed_ids: Some(vec![message_id.to_string()]),
                    }))
                }
                Err(e) => {
                    error!("Failed to process EML file: {}", e);
                    Ok(Json(EmailProcessingResponse {
                        success: false,
                        message: format!("Failed to process email: {}", e),
                        processed_count: Some(0),
                        processed_ids: None,
                    }))
                }
            }
        }
        "mbox" => {
            match ingest_mbox_file_with_embeddings(
                &*state.qdrant_client,
                &request.file_path,
                &state.embedding_service_url,
            ).await {
                Ok(message_ids) => {
                    info!("Successfully processed MBOX file with {} emails", message_ids.len());
                    Ok(Json(EmailProcessingResponse {
                        success: true,
                        message: format!("Processed {} emails from MBOX file", message_ids.len()),
                        processed_count: Some(message_ids.len() as u32),
                        processed_ids: Some(message_ids.into_iter().map(|id| id.to_string()).collect()),
                    }))
                }
                Err(e) => {
                    error!("Failed to process MBOX file: {}", e);
                    Ok(Json(EmailProcessingResponse {
                        success: false,
                        message: format!("Failed to process MBOX file: {}", e),
                        processed_count: Some(0),
                        processed_ids: None,
                    }))
                }
            }
        }
        _ => {
            warn!("Unsupported file type: {}", request.file_type);
            Ok(Json(EmailProcessingResponse {
                success: false,
                message: "Unsupported file type. Use 'eml' or 'mbox'".to_string(),
                processed_count: Some(0),
                processed_ids: None,
            }))
        }
    }
}

// List processed emails endpoint
async fn list_emails(
    State(state): State<AppState>,
    Query(params): Query<ListEmailsQuery>,
) -> Result<Json<EmailListResponse>, StatusCode> {
    let page = params.page.unwrap_or(1);
    let page_size = params.page_size.unwrap_or(20).min(100); // Cap at 100 items per page
    
    info!("Listing emails: page {}, page_size {}", page, page_size);
    
    match get_recent_messages(&*state.qdrant_client, Some(page_size)).await {
        Ok(messages) => {
            let email_summaries: Vec<EmailSummary> = messages
                .into_iter()
                .map(|msg| EmailSummary {
                    id: msg.id.to_string(),
                    subject: msg.subject,
                    from_address: msg.from_address,
                    to_addresses: msg.to_addresses,
                    sent_date: msg.sent_date.map(|dt| dt.to_rfc3339()),
                    created_at: msg.created_at.to_rfc3339(),
                    preview: msg.cleaned_plain_text_body
                        .as_ref()
                        .map(|content| {
                            if content.len() > 200 {
                                format!("{}...", &content[..200])
                            } else {
                                content.clone()
                            }
                        }),
                })
                .collect();
            
            Ok(Json(EmailListResponse {
                total_count: email_summaries.len() as u64, // TODO: Implement proper count
                emails: email_summaries,
                page,
                page_size,
            }))
        }
        Err(e) => {
            error!("Failed to retrieve emails: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// Get email details endpoint
async fn get_email_details(
    State(state): State<AppState>,
    Path(email_id): Path<String>,
) -> Result<Json<EmailDetailResponse>, StatusCode> {
    info!("Getting email details for ID: {}", email_id);
    
    // Parse UUID
    let uuid = match Uuid::parse_str(&email_id) {
        Ok(uuid) => uuid,
        Err(_) => {
            warn!("Invalid email ID format: {}", email_id);
            return Err(StatusCode::BAD_REQUEST);
        }
    };
    
    // For now, we'll search through recent messages to find the one with matching ID
    // In a production system, you'd want a more efficient lookup
    match get_recent_messages(&*state.qdrant_client, Some(1000)).await {
        Ok(messages) => {
            if let Some(message) = messages.into_iter().find(|msg| msg.id == uuid) {
                Ok(Json(EmailDetailResponse { email: message }))
            } else {
                warn!("Email not found: {}", email_id);
                Err(StatusCode::NOT_FOUND)
            }
        }
        Err(e) => {
            error!("Failed to retrieve email details: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// Search emails endpoint
async fn search_emails(
    State(state): State<AppState>,
    Query(params): Query<SearchEmailsQuery>,
) -> Result<Json<SearchEmailsResponse>, StatusCode> {
    let limit = params.limit.unwrap_or(10).min(50); // Cap at 50 results
    let score_threshold = params.score_threshold.unwrap_or(0.5);

    info!("Searching emails with query: '{}', limit: {}, threshold: {}",
          params.query, limit, score_threshold);

    // Generate embedding for the search query
    match get_embedding(&params.query, &state.embedding_service_url).await {
        Ok(query_embedding) => {
            match search_similar_messages(
                &*state.qdrant_client,
                query_embedding,
                Some(limit),
                Some(score_threshold),
            ).await {
                Ok(search_results) => {
                    let result_summaries: Vec<SearchResultSummary> = search_results
                        .into_iter()
                        .map(|result| SearchResultSummary {
                            email: EmailSummary {
                                id: result.message.id.to_string(),
                                subject: result.message.subject,
                                from_address: result.message.from_address,
                                to_addresses: result.message.to_addresses,
                                sent_date: result.message.sent_date.map(|dt| dt.to_rfc3339()),
                                created_at: result.message.created_at.to_rfc3339(),
                                preview: result.message.cleaned_plain_text_body
                                    .as_ref()
                                    .map(|content| {
                                        if content.len() > 200 {
                                            format!("{}...", &content[..200])
                                        } else {
                                            content.clone()
                                        }
                                    }),
                            },
                            score: result.score,
                        })
                        .collect();

                    Ok(Json(SearchEmailsResponse {
                        results: result_summaries.clone(),
                        query: params.query,
                        total_found: result_summaries.len(),
                    }))
                }
                Err(e) => {
                    error!("Failed to search emails: {}", e);
                    Err(StatusCode::INTERNAL_SERVER_ERROR)
                }
            }
        }
        Err(e) => {
            error!("Failed to generate embedding for search query: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// Create the router
fn create_router(state: AppState) -> Router {
    Router::new()
        .route("/health", get(health_check))
        .route("/process", post(process_emails))
        .route("/search", get(search_emails))
        .route("/emails", get(list_emails))
        .route("/emails/:id", get(get_email_details))
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive())
        )
        .with_state(state)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    info!("Starting AI Email Assistant - Ingestion Service HTTP Server");

    // Load environment variables
    dotenv::dotenv().ok();

    // Establish Qdrant connection
    let qdrant_client = match establish_connection().await {
        Ok(client) => {
            info!("✓ Qdrant connection established");
            Arc::new(client)
        }
        Err(e) => {
            error!("⚠ Qdrant connection failed: {}", e);
            error!("  Make sure Qdrant is running on http://localhost:6334");
            return Err(e);
        }
    };

    // Setup collections
    if let Err(e) = setup_collections(&qdrant_client).await {
        warn!("Warning: Collection setup failed: {}", e);
    } else {
        info!("✓ Qdrant collections ready");
    }

    // Get embedding service URL
    let embedding_service_url = std::env::var("EMBEDDING_SERVICE_URL")
        .unwrap_or_else(|_| "http://localhost:8003".to_string());

    info!("Using embedding service at: {}", embedding_service_url);

    // Create application state
    let app_state = AppState {
        qdrant_client,
        embedding_service_url,
    };

    // Create the router
    let app = create_router(app_state);

    // Start the server
    let port = std::env::var("PORT")
        .unwrap_or_else(|_| "8080".to_string())
        .parse::<u16>()
        .unwrap_or(8080);

    let addr = format!("0.0.0.0:{}", port);
    info!("🚀 Server starting on http://{}", addr);

    let listener = tokio::net::TcpListener::bind(&addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
