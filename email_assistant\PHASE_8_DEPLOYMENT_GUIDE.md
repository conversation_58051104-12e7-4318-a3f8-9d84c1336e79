# Phase 8: Local Deployment and Testing - Complete Guide

## Overview

Phase 8 provides comprehensive local deployment orchestration, testing infrastructure, and performance baselines for the AI-Assisted Email Response System. This guide covers all aspects of deploying, testing, and monitoring the system in a local development environment.

## 🚀 Quick Start

### 1. Service Orchestration
```powershell
# Start all services with health monitoring
.\scripts\orchestrate_services.ps1 -Start

# Check service status
.\scripts\orchestrate_services.ps1 -Status -Verbose

# Stop all services gracefully
.\scripts\orchestrate_services.ps1 -Stop
```

### 2. Run Comprehensive Tests
```powershell
# Run all tests with coverage
.\scripts\run_tests.ps1 -All -Coverage

# Run specific component tests
.\scripts\run_tests.ps1 -Python -Unit -Integration

# Run security scans
.\scripts\run_tests.ps1 -Security
```

### 3. Performance Benchmarking
```powershell
# Run all performance benchmarks
.\scripts\benchmark_performance.ps1 -All -Iterations 20

# Specific service benchmarks
.\scripts\benchmark_performance.ps1 -RAG -Verbose
```

## 📋 Service Management

### Enhanced Orchestration Features

The `orchestrate_services.ps1` script provides:

- **Dependency Management**: Services start in correct order based on dependencies
- **Health Monitoring**: Automated health checks with retry logic
- **Graceful Shutdown**: Proper cleanup and resource management
- **Service Discovery**: Automatic configuration and port management
- **Comprehensive Logging**: Detailed orchestration logs for troubleshooting

### Service Dependencies
```
qdrant (Vector Database) → No dependencies
ollama (LLM Service) → No dependencies  
rag_service (Python) → Depends on ollama
ingestion_service (Rust) → Depends on qdrant, rag_service
```

### Service Management Commands
```powershell
# Start specific service
.\scripts\orchestrate_services.ps1 -Service rag_service -Start

# Restart all services
.\scripts\orchestrate_services.ps1 -Restart

# Health check with system metrics
.\scripts\orchestrate_services.ps1 -Health

# View orchestration logs
.\scripts\orchestrate_services.ps1 -Logs
```

## 🧪 Testing Infrastructure

### Test Categories

#### 1. Unit Tests
- **Rust Components**: `cargo test` with coverage via `cargo-llvm-cov`
- **Python Components**: `pytest` with coverage via `pytest-cov`
- **Desktop App**: `npm test` for frontend components

#### 2. Integration Tests
- **Service Communication**: Cross-service API testing
- **Data Flow Validation**: End-to-end data processing
- **Compliance Integration**: Logging and pseudonymisation workflows

#### 3. Performance Tests
- **Load Testing**: Concurrent request handling
- **Benchmark Baselines**: Response time and throughput metrics
- **Resource Monitoring**: CPU and memory usage tracking

#### 4. Security Tests
- **Vulnerability Scanning**: Dependency security audits
- **Code Analysis**: Static security analysis
- **Compliance Validation**: GDPR and EU AI Act requirements

### Running Tests

#### Comprehensive Test Suite
```powershell
# All tests with coverage and security
.\scripts\run_tests.ps1 -All -Coverage -Security -Verbose

# Component-specific testing
.\scripts\run_tests.ps1 -Component rag_service -Unit -Integration -Coverage

# Language-specific testing
.\scripts\run_tests.ps1 -Rust -Unit -Coverage
.\scripts\run_tests.ps1 -Python -Integration -Performance
```

#### Integration Tests
```powershell
# Run integration test suite
cd tests
python -m pytest integration_test_suite.py -v -s

# Specific integration test categories
python -m pytest integration_test_suite.py::TestServiceIntegration -v
python -m pytest integration_test_suite.py::TestComplianceIntegration -v
```

### Test Coverage Requirements

- **Rust Components**: Target 80%+ line coverage
- **Python Components**: Target 85%+ line coverage
- **Integration Tests**: Cover all service interactions
- **End-to-End Tests**: Validate complete user workflows

## 📊 Performance Benchmarking

### Benchmark Categories

#### 1. Ingestion Service Performance
- **Email Processing**: Parse and store email performance
- **Vector Indexing**: Embedding generation and storage
- **Concurrent Ingestion**: Multi-threaded processing capabilities

#### 2. RAG Service Performance
- **Draft Generation**: End-to-end response generation time
- **Context Retrieval**: Vector similarity search performance
- **LLM Integration**: Ollama/Llama-3 response times

#### 3. End-to-End Workflow
- **Complete Pipeline**: Ingestion → Context Retrieval → Draft Generation
- **System Resource Usage**: Memory and CPU consumption
- **Throughput Metrics**: Operations per second under load

### Running Benchmarks

```powershell
# Complete performance suite
.\scripts\benchmark_performance.ps1 -All -Iterations 50 -Verbose

# Service-specific benchmarks
.\scripts\benchmark_performance.ps1 -RAG -Iterations 20
.\scripts\benchmark_performance.ps1 -Ingestion -Concurrency 5

# End-to-end workflow benchmark
.\scripts\benchmark_performance.ps1 -EndToEnd -Iterations 10
```

### Performance Baselines

#### Expected Performance Metrics
- **RAG Draft Generation**: < 15 seconds average
- **Email Ingestion**: < 2 seconds per email
- **Vector Search**: < 500ms for similarity queries
- **End-to-End Workflow**: < 20 seconds total

#### System Requirements
- **Memory Usage**: < 4GB total system usage
- **CPU Usage**: < 80% during normal operations
- **Disk I/O**: Efficient with SSD storage
- **Network**: Local services, minimal network overhead

## 🔒 Security and Compliance

### Security Scanning

#### Dependency Audits
```powershell
# Comprehensive security scan
.\scripts\run_tests.ps1 -Security

# Manual security audits
cd ingestion_service && cargo audit
cd rag_service && python -m safety check
cd desktop_app && npm audit
```

#### Vulnerability Management
- **Rust**: `cargo-audit` for known vulnerabilities
- **Python**: `safety` for dependency security
- **Node.js**: `npm audit` for frontend dependencies
- **Regular Updates**: Automated dependency update checks

### Compliance Validation

#### GDPR Compliance
- **Pseudonymisation Testing**: Validate PII protection
- **Data Minimisation**: Verify minimal data logging
- **Audit Trail**: Comprehensive compliance logging

#### EU AI Act Compliance
- **Output Logging**: 6+ month retention validation
- **Human-in-the-Loop**: Manual review requirement verification
- **Transparency**: Source citation and model metadata

## 📁 File Structure

```
email_assistant/
├── scripts/
│   ├── orchestrate_services.ps1      # Enhanced service management
│   ├── run_tests.ps1                 # Comprehensive testing suite
│   └── benchmark_performance.ps1     # Performance benchmarking
├── tests/
│   └── integration_test_suite.py     # Integration test framework
├── rag_service/
│   └── tests/
│       ├── __init__.py
│       └── test_rag_pipeline.py      # Enhanced unit tests
├── test_results/                     # Test execution results
│   ├── coverage/                     # Code coverage reports
│   └── test_execution.log           # Test execution logs
├── benchmark_results/                # Performance benchmark data
│   ├── benchmark_results_*.json     # Detailed benchmark results
│   └── baseline_performance.json    # Performance baselines
└── logs/
    ├── orchestration.log            # Service orchestration logs
    └── compliance_audit.log         # Compliance audit trail
```

## 🔧 Configuration

### Environment Variables

#### Service Configuration
```bash
# Service endpoints
RAG_SERVICE_HOST=0.0.0.0
RAG_SERVICE_PORT=8003
INGESTION_SERVICE_PORT=8080

# External services
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama3
```

#### Testing Configuration
```bash
# Test execution
TEST_TIMEOUT=60
TEST_CONCURRENCY=5
COVERAGE_THRESHOLD=80

# Performance benchmarks
BENCHMARK_ITERATIONS=10
BENCHMARK_TIMEOUT=120
```

#### Compliance Configuration
```bash
# Compliance logging
COMPLIANCE_LOG_DIR=logs
COMPLIANCE_LOG_LEVEL=INFO
COMPLIANCE_LOG_RETENTION_DAYS=200

# Pseudonymisation
PSEUDONYMISATION_ENABLED=false
PSEUDONYMISATION_LEVEL=disabled
```

## 🚨 Troubleshooting

### Common Issues

#### Service Startup Problems
```powershell
# Check service dependencies
.\scripts\orchestrate_services.ps1 -Status -Verbose

# View detailed logs
.\scripts\orchestrate_services.ps1 -Logs

# Restart specific service
.\scripts\orchestrate_services.ps1 -Service rag_service -Restart
```

#### Test Failures
```powershell
# Run tests with verbose output
.\scripts\run_tests.ps1 -All -Verbose

# Check test logs
Get-Content test_results\test_execution.log -Tail 50

# Run specific failing test
.\scripts\run_tests.ps1 -Component rag_service -Unit
```

#### Performance Issues
```powershell
# Run performance diagnostics
.\scripts\benchmark_performance.ps1 -All -Verbose

# Check system resources
.\scripts\orchestrate_services.ps1 -Health

# Monitor service logs
Get-Content logs\orchestration.log -Wait
```

### Log Analysis

#### Service Logs
- **Orchestration**: `logs/orchestration.log`
- **Compliance**: `logs/compliance_audit.log`
- **Test Execution**: `test_results/test_execution.log`
- **Benchmark Results**: `benchmark_results/benchmark_results_*.json`

#### Performance Monitoring
- **System Metrics**: CPU, memory, disk usage
- **Service Health**: Response times, error rates
- **Resource Usage**: Per-service resource consumption

## 📈 Monitoring and Alerting

### Health Monitoring
```powershell
# Continuous health monitoring
while ($true) {
    .\scripts\orchestrate_services.ps1 -Health
    Start-Sleep -Seconds 30
}
```

### Performance Monitoring
```powershell
# Regular performance checks
.\scripts\benchmark_performance.ps1 -RAG -Iterations 5
```

### Compliance Monitoring
```powershell
# Check compliance logs
Get-Content logs\compliance_audit.log | Select-String "ERROR"
```

## 🎯 Success Criteria

### Deployment Success
- ✅ All services start successfully with dependencies
- ✅ Health checks pass for all components
- ✅ Service orchestration works reliably
- ✅ Graceful shutdown and cleanup functions

### Testing Success
- ✅ Unit test coverage > 80% for all components
- ✅ Integration tests pass for all service interactions
- ✅ Security scans show no critical vulnerabilities
- ✅ Compliance features validated and working

### Performance Success
- ✅ RAG draft generation < 15 seconds average
- ✅ Email ingestion < 2 seconds per email
- ✅ System handles 5+ concurrent requests
- ✅ Memory usage < 4GB total system

### Compliance Success
- ✅ EU AI Act logging requirements met
- ✅ GDPR pseudonymisation infrastructure ready
- ✅ Audit trail comprehensive and accessible
- ✅ Security scanning integrated and automated

---

**Phase 8 Status: ✅ COMPLETE**

The AI-Assisted Email Response System now includes comprehensive local deployment orchestration, testing infrastructure, and performance monitoring capabilities, making it production-ready for local development and testing environments.
