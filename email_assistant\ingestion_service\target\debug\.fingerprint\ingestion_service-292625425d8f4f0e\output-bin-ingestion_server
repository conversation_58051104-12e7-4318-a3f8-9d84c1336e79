{"$message_type":"diagnostic","message":"the trait bound `fn(State<...>, ...) -> ... {process_emails}: <PERSON><PERSON><_, _>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src\\server.rs","byte_start":12597,"byte_end":12611,"line_start":357,"line_end":357,"column_start":33,"column_end":47,"is_primary":true,"text":[{"text":"        .route(\"/process\", post(process_emails))","highlight_start":33,"highlight_end":47}],"label":"the trait `Handler<_, _>` is not implemented for fn item `fn(State<AppState>, Json<...>) -> ... {process_emails}`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server.rs","byte_start":12592,"byte_end":12596,"line_start":357,"line_end":357,"column_start":28,"column_end":32,"is_primary":false,"text":[{"text":"        .route(\"/process\", post(process_emails))","highlight_start":28,"highlight_end":32}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Consider using `#[axum::debug_handler]` to improve the error message","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Handler<T, S>`:\n  `MethodRouter<S>` implements `Handler<(), S>`\n  `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `post`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":12739,"byte_end":12743,"line_start":443,"line_end":443,"column_start":23,"column_end":27,"is_primary":false,"text":[{"text":"top_level_handler_fn!(post, POST);","highlight_start":23,"highlight_end":27}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":4729,"byte_end":4742,"line_start":166,"line_end":166,"column_start":16,"column_end":29,"is_primary":true,"text":[{"text":"            H: Handler<T, S>,","highlight_start":16,"highlight_end":29}],"label":"required by this bound in `post`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":4296,"byte_end":4524,"line_start":150,"line_end":156,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        top_level_handler_fn!(","highlight_start":9,"highlight_end":31},{"text":"            #[doc = concat!(\"Route `\", stringify!($method) ,\"` requests to the given handler.\")]","highlight_start":1,"highlight_end":97},{"text":"            ///","highlight_start":1,"highlight_end":16},{"text":"            /// See [`get`] for an example.","highlight_start":1,"highlight_end":44},{"text":"            $name,","highlight_start":1,"highlight_end":19},{"text":"            $method","highlight_start":1,"highlight_end":20},{"text":"        );","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":12717,"byte_end":12750,"line_start":443,"line_end":443,"column_start":1,"column_end":34,"is_primary":false,"text":[{"text":"top_level_handler_fn!(post, POST);","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"top_level_handler_fn!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":3010,"byte_end":3043,"line_start":104,"line_end":104,"column_start":1,"column_end":34,"is_primary":false,"text":[{"text":"macro_rules! top_level_handler_fn {","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"top_level_handler_fn!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":3010,"byte_end":3043,"line_start":104,"line_end":104,"column_start":1,"column_end":34,"is_primary":false,"text":[{"text":"macro_rules! top_level_handler_fn {","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null},{"message":"the full name for the type has been written to 'C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\ingestion_service\\target\\debug\\deps\\ingestion_server.long-type-7031735996134331295.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `fn(State<...>, ...) -> ... {process_emails}: Handler<_, _>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server.rs:357:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m357\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .route(\"/process\", post(process_emails))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Handler<_, _>` is not implemented for fn item `fn(State<AppState>, Json<...>) -> ... {process_emails}`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: Consider using `#[axum::debug_handler]` to improve the error message\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `Handler<T, S>`:\u001b[0m\n\u001b[0m              `MethodRouter<S>` implements `Handler<(), S>`\u001b[0m\n\u001b[0m              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `post`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs:443:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m443\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mtop_level_handler_fn!(post, POST);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `post`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the full name for the type has been written to 'C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\ingestion_service\\target\\debug\\deps\\ingestion_server.long-type-7031735996134331295.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `fn(State<...>, ...) -> ... {search_emails}: Handler<_, _>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src\\server.rs","byte_start":12644,"byte_end":12657,"line_start":358,"line_end":358,"column_start":31,"column_end":44,"is_primary":true,"text":[{"text":"        .route(\"/search\", get(search_emails))","highlight_start":31,"highlight_end":44}],"label":"the trait `Handler<_, _>` is not implemented for fn item `fn(State<AppState>, Query<...>) -> ... {search_emails}`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server.rs","byte_start":12640,"byte_end":12643,"line_start":358,"line_end":358,"column_start":27,"column_end":30,"is_primary":false,"text":[{"text":"        .route(\"/search\", get(search_emails))","highlight_start":27,"highlight_end":30}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"Consider using `#[axum::debug_handler]` to improve the error message","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Handler<T, S>`:\n  `MethodRouter<S>` implements `Handler<(), S>`\n  `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `axum::routing::get`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":12593,"byte_end":12596,"line_start":439,"line_end":439,"column_start":23,"column_end":26,"is_primary":false,"text":[{"text":"top_level_handler_fn!(get, GET);","highlight_start":23,"highlight_end":26}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":4729,"byte_end":4742,"line_start":166,"line_end":166,"column_start":16,"column_end":29,"is_primary":true,"text":[{"text":"            H: Handler<T, S>,","highlight_start":16,"highlight_end":29}],"label":"required by this bound in `get`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":3096,"byte_end":3889,"line_start":108,"line_end":131,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        top_level_handler_fn!(","highlight_start":9,"highlight_end":31},{"text":"            /// Route `GET` requests to the given handler.","highlight_start":1,"highlight_end":59},{"text":"            ///","highlight_start":1,"highlight_end":16},{"text":"            /// # Example","highlight_start":1,"highlight_end":26},{"text":"            ///","highlight_start":1,"highlight_end":16},{"text":"            /// ```rust","highlight_start":1,"highlight_end":24},{"text":"            /// use axum::{","highlight_start":1,"highlight_end":28},{"text":"            ///     routing::get,","highlight_start":1,"highlight_end":34},{"text":"            ///     Router,","highlight_start":1,"highlight_end":28},{"text":"            /// };","highlight_start":1,"highlight_end":19},{"text":"            ///","highlight_start":1,"highlight_end":16},{"text":"            /// async fn handler() {}","highlight_start":1,"highlight_end":38},{"text":"            ///","highlight_start":1,"highlight_end":16},{"text":"            /// // Requests to `GET /` will go to `handler`.","highlight_start":1,"highlight_end":61},{"text":"            /// let app = Router::new().route(\"/\", get(handler));","highlight_start":1,"highlight_end":66},{"text":"            /// # let _: Router = app;","highlight_start":1,"highlight_end":39},{"text":"            /// ```","highlight_start":1,"highlight_end":20},{"text":"            ///","highlight_start":1,"highlight_end":16},{"text":"            /// Note that `get` routes will also be called for `HEAD` requests but will have","highlight_start":1,"highlight_end":93},{"text":"            /// the response body removed. Make sure to add explicit `HEAD` routes","highlight_start":1,"highlight_end":83},{"text":"            /// afterwards.","highlight_start":1,"highlight_end":28},{"text":"            $name,","highlight_start":1,"highlight_end":19},{"text":"            GET","highlight_start":1,"highlight_end":16},{"text":"        );","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":12571,"byte_end":12602,"line_start":439,"line_end":439,"column_start":1,"column_end":32,"is_primary":false,"text":[{"text":"top_level_handler_fn!(get, GET);","highlight_start":1,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"top_level_handler_fn!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":3010,"byte_end":3043,"line_start":104,"line_end":104,"column_start":1,"column_end":34,"is_primary":false,"text":[{"text":"macro_rules! top_level_handler_fn {","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"top_level_handler_fn!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs","byte_start":3010,"byte_end":3043,"line_start":104,"line_end":104,"column_start":1,"column_end":34,"is_primary":false,"text":[{"text":"macro_rules! top_level_handler_fn {","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null},{"message":"the full name for the type has been written to 'C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\ingestion_service\\target\\debug\\deps\\ingestion_server.long-type-14427170167016576787.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `fn(State<...>, ...) -> ... {search_emails}: Handler<_, _>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server.rs:358:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .route(\"/search\", get(search_emails))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Handler<_, _>` is not implemented for fn item `fn(State<AppState>, Query<...>) -> ... {search_emails}`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: Consider using `#[axum::debug_handler]` to improve the error message\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `Handler<T, S>`:\u001b[0m\n\u001b[0m              `MethodRouter<S>` implements `Handler<(), S>`\u001b[0m\n\u001b[0m              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `axum::routing::get`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\axum-0.7.9\\src\\routing\\method_routing.rs:439:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m439\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mtop_level_handler_fn!(get, GET);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `get`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the full name for the type has been written to 'C:\\Users\\<USER>\\Desktop\\thunder\\email_assistant\\ingestion_service\\target\\debug\\deps\\ingestion_server.long-type-14427170167016576787.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0277`.\u001b[0m\n"}
