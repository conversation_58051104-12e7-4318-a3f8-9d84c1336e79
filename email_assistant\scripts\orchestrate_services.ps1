# AI-Assisted Email Response System - Enhanced Service Orchestration
# Phase 8: Local Deployment and Testing
# 
# This script provides comprehensive service management with:
# - Dependency checking and validation
# - Health monitoring and retry logic
# - Graceful startup sequencing
# - Service discovery and configuration
# - Comprehensive error handling and logging

param(
    [switch]$Start,
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Status,
    [switch]$Health,
    [switch]$Logs,
    [switch]$Help,
    [string]$Service = "",
    [int]$Timeout = 60,
    [switch]$Verbose
)

# Configuration
$script:ProjectRoot = Split-Path (Split-Path $MyInvocation.MyCommand.Path)
$script:LogFile = Join-Path $ProjectRoot "logs\orchestration.log"
$script:PidFile = Join-Path $ProjectRoot "logs\services.pid"

# Service definitions with dependencies and health checks
$script:Services = @{
    "qdrant" = @{
        Name = "Qdrant Vector Database"
        Port = 6333
        HealthUrl = "http://localhost:6333/"
        WorkingDir = Join-Path $ProjectRoot "ingestion_service"
        Command = ".\qdrant\qdrant.exe"
        Dependencies = @()
        StartupTime = 10
        Critical = $true
    }
    "ollama" = @{
        Name = "Ollama LLM Service"
        Port = 11434
        HealthUrl = "http://localhost:11434/api/tags"
        WorkingDir = $env:USERPROFILE
        Command = "ollama serve"
        Dependencies = @()
        StartupTime = 15
        Critical = $true
    }
    "rag_service" = @{
        Name = "RAG Service (Python)"
        Port = 8003
        HealthUrl = "http://localhost:8003/health"
        WorkingDir = Join-Path $ProjectRoot "rag_service"
        Command = "python main.py"
        Dependencies = @("ollama")
        StartupTime = 20
        Critical = $true
    }
    "ingestion_service" = @{
        Name = "Ingestion Service (Rust)"
        Port = 8080
        HealthUrl = "http://localhost:8080/health"
        WorkingDir = Join-Path $ProjectRoot "ingestion_service"
        Command = "python main.py"
        Dependencies = @("qdrant", "rag_service")
        StartupTime = 15
        Critical = $false
    }
}

# Startup sequence based on dependencies
$script:StartupOrder = @("qdrant", "ollama", "rag_service", "ingestion_service")

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Ensure logs directory exists
    $logsDir = Split-Path $script:LogFile
    if (-not (Test-Path $logsDir)) {
        New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
    }
    
    # Write to log file
    Add-Content -Path $script:LogFile -Value $logEntry
    
    # Write to console with colors
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARN" { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        "INFO" { Write-Host $logEntry -ForegroundColor Cyan }
        default { Write-Host $logEntry }
    }
}

function Test-Port {
    param([int]$Port, [int]$TimeoutSeconds = 5)
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect("localhost", $Port, $null, $null)
        $wait = $asyncResult.AsyncWaitHandle.WaitOne($TimeoutSeconds * 1000, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            return $true
        } else {
            $tcpClient.Close()
            return $false
        }
    } catch {
        return $false
    }
}

function Test-ServiceHealth {
    param([hashtable]$ServiceConfig, [int]$TimeoutSeconds = 10)
    
    if (-not $ServiceConfig.HealthUrl) {
        return Test-Port -Port $ServiceConfig.Port -TimeoutSeconds $TimeoutSeconds
    }
    
    try {
        $response = Invoke-WebRequest -Uri $ServiceConfig.HealthUrl -TimeoutSec $TimeoutSeconds -ErrorAction Stop
        return $response.StatusCode -eq 200
    } catch {
        if ($script:Verbose) {
            Write-Log "Health check failed for $($ServiceConfig.Name): $_" "WARN"
        }
        return $false
    }
}

function Wait-ForService {
    param([hashtable]$ServiceConfig, [int]$MaxWaitSeconds = 60)
    
    Write-Log "Waiting for $($ServiceConfig.Name) to become healthy..."
    
    $elapsed = 0
    $interval = 2
    
    while ($elapsed -lt $MaxWaitSeconds) {
        if (Test-ServiceHealth -ServiceConfig $ServiceConfig) {
            Write-Log "$($ServiceConfig.Name) is healthy" "SUCCESS"
            return $true
        }
        
        Start-Sleep -Seconds $interval
        $elapsed += $interval
        
        if ($script:Verbose) {
            Write-Log "Waiting for $($ServiceConfig.Name)... ($elapsed/$MaxWaitSeconds seconds)"
        }
    }
    
    Write-Log "$($ServiceConfig.Name) failed to become healthy within $MaxWaitSeconds seconds" "ERROR"
    return $false
}

function Start-ServiceProcess {
    param([string]$ServiceName, [hashtable]$ServiceConfig)
    
    Write-Log "Starting $($ServiceConfig.Name)..."
    
    # Check if already running
    if (Test-ServiceHealth -ServiceConfig $ServiceConfig -TimeoutSeconds 2) {
        Write-Log "$($ServiceConfig.Name) is already running" "SUCCESS"
        return $true
    }
    
    # Validate working directory
    if (-not (Test-Path $ServiceConfig.WorkingDir)) {
        Write-Log "Working directory not found: $($ServiceConfig.WorkingDir)" "ERROR"
        return $false
    }
    
    try {
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "powershell.exe"
        $startInfo.Arguments = "-NoExit -Command `"cd '$($ServiceConfig.WorkingDir)'; $($ServiceConfig.Command)`""
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal
        $startInfo.CreateNoWindow = $false
        
        $process = [System.Diagnostics.Process]::Start($startInfo)
        
        # Store process ID for later management
        $pidData = @{
            ServiceName = $ServiceName
            ProcessId = $process.Id
            StartTime = Get-Date
        }
        
        # Save PID information
        $pidData | ConvertTo-Json | Add-Content -Path $script:PidFile
        
        Write-Log "$($ServiceConfig.Name) started with PID $($process.Id)" "SUCCESS"
        
        # Wait for service to become healthy
        if (-not (Wait-ForService -ServiceConfig $ServiceConfig -MaxWaitSeconds $ServiceConfig.StartupTime)) {
            Write-Log "Failed to start $($ServiceConfig.Name) - health check failed" "ERROR"
            return $false
        }
        
        return $true
        
    } catch {
        Write-Log "Failed to start $($ServiceConfig.Name): $_" "ERROR"
        return $false
    }
}

function Stop-ServiceProcess {
    param([string]$ServiceName)
    
    $serviceConfig = $script:Services[$ServiceName]
    Write-Log "Stopping $($serviceConfig.Name)..."
    
    # Try graceful shutdown first by checking port
    if (-not (Test-Port -Port $serviceConfig.Port -TimeoutSeconds 2)) {
        Write-Log "$($serviceConfig.Name) is not running" "INFO"
        return $true
    }
    
    # Find and stop processes
    $stopped = $false
    
    # Try to find by command pattern
    $processes = Get-Process | Where-Object { 
        $_.ProcessName -like "*python*" -or 
        $_.ProcessName -like "*ollama*" -or 
        $_.ProcessName -like "*qdrant*" -or
        $_.ProcessName -like "*powershell*"
    }
    
    foreach ($process in $processes) {
        try {
            if ($process.CommandLine -and $process.CommandLine.Contains($serviceConfig.Command.Split(' ')[0])) {
                Write-Log "Stopping process $($process.Id) for $($serviceConfig.Name)"
                $process.Kill()
                $stopped = $true
            }
        } catch {
            # Process might have already exited
        }
    }
    
    # Wait for port to be released
    $maxWait = 10
    $elapsed = 0
    while ((Test-Port -Port $serviceConfig.Port -TimeoutSeconds 1) -and $elapsed -lt $maxWait) {
        Start-Sleep -Seconds 1
        $elapsed++
    }
    
    if (Test-Port -Port $serviceConfig.Port -TimeoutSeconds 1) {
        Write-Log "Failed to stop $($serviceConfig.Name) - port still in use" "WARN"
        return $false
    } else {
        Write-Log "$($serviceConfig.Name) stopped successfully" "SUCCESS"
        return $true
    }
}

function Get-ServiceStatus {
    param([string]$ServiceName = "")
    
    Write-Host "`n=== Service Status ===" -ForegroundColor Cyan
    
    $servicesToCheck = if ($ServiceName) { @($ServiceName) } else { $script:StartupOrder }
    
    foreach ($name in $servicesToCheck) {
        $config = $script:Services[$name]
        $isHealthy = Test-ServiceHealth -ServiceConfig $config -TimeoutSeconds 3
        $portOpen = Test-Port -Port $config.Port -TimeoutSeconds 2
        
        $status = if ($isHealthy) { "✅ HEALTHY" } elseif ($portOpen) { "⚠️ RUNNING" } else { "❌ STOPPED" }
        $color = if ($isHealthy) { "Green" } elseif ($portOpen) { "Yellow" } else { "Red" }
        
        Write-Host "$($config.Name) (Port $($config.Port)): $status" -ForegroundColor $color
        
        if ($script:Verbose -and $config.HealthUrl) {
            try {
                $response = Invoke-WebRequest -Uri $config.HealthUrl -TimeoutSec 3 -ErrorAction Stop
                $healthData = $response.Content | ConvertFrom-Json -ErrorAction SilentlyContinue
                if ($healthData) {
                    Write-Host "  Health Details: $($healthData | ConvertTo-Json -Compress)" -ForegroundColor Gray
                }
            } catch {
                # Health endpoint not available
            }
        }
    }
}

function Start-AllServices {
    Write-Log "Starting all services in dependency order..." "INFO"
    
    # Clear old PID file
    if (Test-Path $script:PidFile) {
        Remove-Item $script:PidFile -Force
    }
    
    $success = $true
    
    foreach ($serviceName in $script:StartupOrder) {
        $serviceConfig = $script:Services[$serviceName]
        
        # Check dependencies
        foreach ($dependency in $serviceConfig.Dependencies) {
            $depConfig = $script:Services[$dependency]
            if (-not (Test-ServiceHealth -ServiceConfig $depConfig -TimeoutSeconds 5)) {
                Write-Log "Dependency $($depConfig.Name) is not healthy, cannot start $($serviceConfig.Name)" "ERROR"
                $success = $false
                break
            }
        }
        
        if (-not $success -and $serviceConfig.Critical) {
            Write-Log "Critical service dependency failed, aborting startup" "ERROR"
            break
        }
        
        if (-not (Start-ServiceProcess -ServiceName $serviceName -ServiceConfig $serviceConfig)) {
            if ($serviceConfig.Critical) {
                Write-Log "Critical service $($serviceConfig.Name) failed to start, aborting" "ERROR"
                $success = $false
                break
            } else {
                Write-Log "Non-critical service $($serviceConfig.Name) failed to start, continuing" "WARN"
            }
        }
        
        # Brief pause between service starts
        Start-Sleep -Seconds 2
    }
    
    if ($success) {
        Write-Log "All services started successfully" "SUCCESS"
        Get-ServiceStatus
    } else {
        Write-Log "Service startup failed" "ERROR"
        return $false
    }
    
    return $success
}

function Stop-AllServices {
    Write-Log "Stopping all services..." "INFO"
    
    # Stop in reverse order
    $stopOrder = $script:StartupOrder | Sort-Object { $script:StartupOrder.IndexOf($_) } -Descending
    
    foreach ($serviceName in $stopOrder) {
        Stop-ServiceProcess -ServiceName $serviceName
        Start-Sleep -Seconds 1
    }
    
    # Clean up PID file
    if (Test-Path $script:PidFile) {
        Remove-Item $script:PidFile -Force
    }
    
    Write-Log "All services stopped" "SUCCESS"
}

function Show-Help {
    Write-Host @"
AI-Assisted Email Response System - Service Orchestration

USAGE:
    .\orchestrate_services.ps1 [OPTIONS]

OPTIONS:
    -Start              Start all services in dependency order
    -Stop               Stop all services gracefully
    -Restart            Restart all services
    -Status             Show current status of all services
    -Health             Perform comprehensive health checks
    -Logs               Show recent orchestration logs
    -Service <name>     Target specific service (qdrant, ollama, rag_service, ingestion_service)
    -Timeout <seconds>  Set timeout for operations (default: 60)
    -Verbose            Enable verbose output
    -Help               Show this help message

EXAMPLES:
    .\orchestrate_services.ps1 -Start                    # Start all services
    .\orchestrate_services.ps1 -Status -Verbose          # Show detailed status
    .\orchestrate_services.ps1 -Service rag_service -Start  # Start only RAG service
    .\orchestrate_services.ps1 -Stop                     # Stop all services
    .\orchestrate_services.ps1 -Health                   # Run health checks

SERVICE DEPENDENCIES:
    qdrant (Vector Database) → No dependencies
    ollama (LLM Service) → No dependencies  
    rag_service (Python) → Depends on ollama
    ingestion_service (Rust) → Depends on qdrant, rag_service

"@ -ForegroundColor White
}

# Main execution logic
if ($Help) {
    Show-Help
    exit 0
}

Write-Host "=== AI Email Assistant - Service Orchestration ===" -ForegroundColor Green
Write-Log "Orchestration script started with parameters: $($PSBoundParameters | ConvertTo-Json -Compress)"

try {
    if ($Start) {
        if ($Service) {
            $serviceConfig = $script:Services[$Service]
            if ($serviceConfig) {
                Start-ServiceProcess -ServiceName $Service -ServiceConfig $serviceConfig
            } else {
                Write-Log "Unknown service: $Service" "ERROR"
                exit 1
            }
        } else {
            Start-AllServices
        }
    }
    elseif ($Stop) {
        if ($Service) {
            Stop-ServiceProcess -ServiceName $Service
        } else {
            Stop-AllServices
        }
    }
    elseif ($Restart) {
        if ($Service) {
            Stop-ServiceProcess -ServiceName $Service
            Start-Sleep -Seconds 3
            $serviceConfig = $script:Services[$Service]
            Start-ServiceProcess -ServiceName $Service -ServiceConfig $serviceConfig
        } else {
            Stop-AllServices
            Start-Sleep -Seconds 5
            Start-AllServices
        }
    }
    elseif ($Status) {
        Get-ServiceStatus -ServiceName $Service
    }
    elseif ($Health) {
        Write-Host "`n=== Comprehensive Health Check ===" -ForegroundColor Cyan
        Get-ServiceStatus
        
        # Additional health checks
        Write-Host "`n=== System Resources ===" -ForegroundColor Cyan
        $memory = Get-WmiObject -Class Win32_OperatingSystem
        $memoryUsage = [math]::Round(($memory.TotalVisibleMemorySize - $memory.FreePhysicalMemory) / $memory.TotalVisibleMemorySize * 100, 2)
        Write-Host "Memory Usage: $memoryUsage%" -ForegroundColor $(if ($memoryUsage -gt 80) { "Red" } elseif ($memoryUsage -gt 60) { "Yellow" } else { "Green" })
        
        $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
        $diskUsage = [math]::Round(($disk.Size - $disk.FreeSpace) / $disk.Size * 100, 2)
        Write-Host "Disk Usage (C:): $diskUsage%" -ForegroundColor $(if ($diskUsage -gt 90) { "Red" } elseif ($diskUsage -gt 75) { "Yellow" } else { "Green" })
    }
    elseif ($Logs) {
        if (Test-Path $script:LogFile) {
            Write-Host "`n=== Recent Orchestration Logs ===" -ForegroundColor Cyan
            Get-Content $script:LogFile -Tail 20
        } else {
            Write-Host "No log file found at $script:LogFile" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "No action specified. Use -Help for usage information." -ForegroundColor Yellow
        Get-ServiceStatus
    }
    
} catch {
    Write-Log "Orchestration script failed: $_" "ERROR"
    exit 1
}

Write-Log "Orchestration script completed"
