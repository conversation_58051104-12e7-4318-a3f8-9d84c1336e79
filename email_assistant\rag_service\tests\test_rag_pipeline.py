"""
Unit tests for RAG Pipeline

Tests the core RAG pipeline functionality including:
- Email embedding generation
- Vector similarity search
- Context formatting
- LLM prompt composition
- Draft generation workflow
"""

import pytest
import asyncio
import numpy as np
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import sys
import os

# Add the rag_service directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rag_pipeline import RAGPipeline


class TestRAGPipeline:
    """Test suite for RAG Pipeline functionality"""
    
    @pytest.fixture
    def mock_embedding_model(self):
        """Mock embedding model for testing"""
        mock_model = Mock()
        mock_model.encode.return_value = np.array([0.1, 0.2, 0.3, 0.4])
        return mock_model
    
    @pytest.fixture
    def mock_ollama_llm(self):
        """Mock Ollama LLM for testing"""
        mock_llm = AsyncMock()
        mock_llm.ainvoke.return_value = "This is a test draft response."
        return mock_llm
    
    @pytest.fixture
    def rag_pipeline(self, mock_embedding_model, mock_ollama_llm):
        """Create RAG pipeline with mocked dependencies"""
        with patch('rag_pipeline.SentenceTransformer', return_value=mock_embedding_model), \
             patch('rag_pipeline.OllamaLLM', return_value=mock_ollama_llm):
            
            pipeline = RAGPipeline(
                ollama_base_url="http://localhost:11434",
                ollama_model="llama3",
                ingestion_service_url="http://localhost:8080",
                embedding_model_name="test-model"
            )
            return pipeline
    
    @pytest.mark.asyncio
    async def test_generate_embeddings(self, rag_pipeline, mock_embedding_model):
        """Test embedding generation for email text"""
        test_text = "Subject: Test Email\nContent: This is a test email."
        
        result = await rag_pipeline.generate_embeddings(test_text)
        
        assert result is not None
        assert len(result) == 4  # Mock embedding dimension
        assert isinstance(result, np.ndarray)
        mock_embedding_model.encode.assert_called_once_with(test_text)
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_empty_text(self, rag_pipeline):
        """Test embedding generation with empty text"""
        result = await rag_pipeline.generate_embeddings("")
        
        assert result is not None
        assert len(result) == 4  # Mock embedding dimension
    
    @pytest.mark.asyncio
    async def test_search_similar_emails_success(self, rag_pipeline):
        """Test successful vector similarity search"""
        mock_embedding = np.array([0.1, 0.2, 0.3, 0.4])
        
        # Mock successful HTTP response
        mock_response_data = {
            "similar_emails": [
                {
                    "id": "email_1",
                    "subject": "Similar Email 1",
                    "content": "This is similar content.",
                    "similarity_score": 0.85
                },
                {
                    "id": "email_2", 
                    "subject": "Similar Email 2",
                    "content": "Another similar email.",
                    "similarity_score": 0.78
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
            
            result = await rag_pipeline.search_similar_emails(mock_embedding)
            
            assert len(result) == 2
            assert result[0]["id"] == "email_1"
            assert result[0]["similarity_score"] == 0.85
            assert result[1]["id"] == "email_2"
            assert result[1]["similarity_score"] == 0.78
    
    @pytest.mark.asyncio
    async def test_search_similar_emails_service_unavailable(self, rag_pipeline):
        """Test vector search when ingestion service is unavailable"""
        mock_embedding = np.array([0.1, 0.2, 0.3, 0.4])
        
        with patch('httpx.AsyncClient') as mock_client:
            # Simulate connection error
            mock_client.return_value.__aenter__.return_value.post.side_effect = Exception("Connection failed")
            
            result = await rag_pipeline.search_similar_emails(mock_embedding)
            
            # Should return empty list when service unavailable
            assert result == []
    
    @pytest.mark.asyncio
    async def test_search_similar_emails_no_results(self, rag_pipeline):
        """Test vector search with no similar emails found"""
        mock_embedding = np.array([0.1, 0.2, 0.3, 0.4])
        
        mock_response_data = {"similar_emails": []}
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
            
            result = await rag_pipeline.search_similar_emails(mock_embedding)
            
            assert result == []
    
    def test_format_context_with_emails(self, rag_pipeline):
        """Test context formatting with similar emails"""
        similar_emails = [
            {
                "id": "email_1",
                "subject": "Contract Review",
                "content": "Please review the attached contract.",
                "similarity_score": 0.85
            },
            {
                "id": "email_2",
                "subject": "Legal Consultation", 
                "content": "I need legal advice on this matter.",
                "similarity_score": 0.78
            }
        ]
        
        result = rag_pipeline._format_context(similar_emails)
        
        assert "Contract Review" in result
        assert "Legal Consultation" in result
        assert "Please review the attached contract." in result
        assert "I need legal advice on this matter." in result
        assert "Email 1:" in result
        assert "Email 2:" in result
    
    def test_format_context_empty(self, rag_pipeline):
        """Test context formatting with no similar emails"""
        result = rag_pipeline._format_context([])
        
        assert result == "No similar emails found in the database."
    
    def test_format_context_truncation(self, rag_pipeline):
        """Test context formatting with content truncation"""
        # Create email with very long content
        long_content = "This is a very long email content. " * 100  # ~3500 characters
        
        similar_emails = [
            {
                "id": "email_1",
                "subject": "Long Email",
                "content": long_content,
                "similarity_score": 0.85
            }
        ]
        
        result = rag_pipeline._format_context(similar_emails)
        
        # Should be truncated to max_context_length (default 2000)
        assert len(result) <= 2500  # Some buffer for formatting
        assert "..." in result  # Truncation indicator
    
    @pytest.mark.asyncio
    async def test_call_llm_success(self, rag_pipeline, mock_ollama_llm):
        """Test successful LLM call"""
        test_prompt = "Generate a professional email response."
        
        result = await rag_pipeline._call_llm(test_prompt)
        
        assert result == "This is a test draft response."
        mock_ollama_llm.ainvoke.assert_called_once_with(test_prompt)
    
    @pytest.mark.asyncio
    async def test_call_llm_failure(self, rag_pipeline, mock_ollama_llm):
        """Test LLM call failure handling"""
        test_prompt = "Generate a professional email response."
        mock_ollama_llm.ainvoke.side_effect = Exception("LLM service unavailable")
        
        with pytest.raises(Exception) as exc_info:
            await rag_pipeline._call_llm(test_prompt)
        
        assert "LLM service unavailable" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_generate_draft_full_workflow(self, rag_pipeline, mock_ollama_llm):
        """Test complete draft generation workflow"""
        # Mock the search_similar_emails method
        mock_similar_emails = [
            {
                "id": "email_1",
                "subject": "Similar Case",
                "content": "Previous legal advice on similar matter.",
                "similarity_score": 0.85
            }
        ]
        
        with patch.object(rag_pipeline, 'search_similar_emails', return_value=mock_similar_emails):
            result = await rag_pipeline.generate_draft(
                subject="Legal Consultation Request",
                sender="<EMAIL>",
                content="I need legal advice on contract terms."
            )
            
            assert "draft" in result
            assert "context_emails_count" in result
            assert "similar_emails" in result
            assert "metadata" in result
            
            assert result["draft"] == "This is a test draft response."
            assert result["context_emails_count"] == 1
            assert len(result["similar_emails"]) == 1
            assert result["metadata"]["model"] == "llama3"
            assert "transaction_id" in result["metadata"]
    
    @pytest.mark.asyncio
    async def test_generate_draft_no_context(self, rag_pipeline, mock_ollama_llm):
        """Test draft generation with no similar emails found"""
        with patch.object(rag_pipeline, 'search_similar_emails', return_value=[]):
            result = await rag_pipeline.generate_draft(
                subject="Unique Legal Question",
                sender="<EMAIL>", 
                content="This is a completely unique legal question."
            )
            
            assert result["context_emails_count"] == 0
            assert result["similar_emails"] == []
            assert result["draft"] == "This is a test draft response."
    
    @pytest.mark.asyncio
    async def test_generate_draft_with_compliance_logging(self, rag_pipeline):
        """Test that compliance logging is integrated into draft generation"""
        with patch('rag_pipeline.get_compliance_logger') as mock_logger_getter:
            mock_logger = Mock()
            mock_logger.generate_transaction_id.return_value = "test-transaction-123"
            mock_logger_getter.return_value = mock_logger
            
            with patch.object(rag_pipeline, 'search_similar_emails', return_value=[]):
                result = await rag_pipeline.generate_draft(
                    subject="Test Subject",
                    sender="<EMAIL>",
                    content="Test content"
                )
                
                # Verify compliance logging was called
                mock_logger.generate_transaction_id.assert_called_once()
                mock_logger.log_draft_request.assert_called_once()
                mock_logger.log_context_retrieval.assert_called_once()
                mock_logger.log_prompt_composition.assert_called_once()
                mock_logger.log_llm_generation.assert_called_once()
                mock_logger.log_draft_response.assert_called_once()
                
                # Verify transaction ID is in metadata
                assert result["metadata"]["transaction_id"] == "test-transaction-123"
    
    @pytest.mark.asyncio
    async def test_close_pipeline(self, rag_pipeline):
        """Test pipeline cleanup"""
        # Should not raise any exceptions
        await rag_pipeline.close()
    
    def test_pipeline_configuration(self, rag_pipeline):
        """Test pipeline configuration properties"""
        assert rag_pipeline.ollama_base_url == "http://localhost:11434"
        assert rag_pipeline.ollama_model == "llama3"
        assert rag_pipeline.ingestion_service_url == "http://localhost:8080"
        assert rag_pipeline.similarity_threshold == 0.7
        assert rag_pipeline.max_context_emails == 5
    
    def test_prompt_template_format(self, rag_pipeline):
        """Test that prompt template is properly formatted"""
        context = "Previous email about contracts."
        subject = "Contract Review"
        sender = "<EMAIL>"
        content = "Please review this contract."
        
        prompt = rag_pipeline.prompt_template.format(
            context=context,
            subject=subject,
            sender=sender,
            content=content
        )
        
        assert context in prompt
        assert subject in prompt
        assert sender in prompt
        assert content in prompt
        assert "legal advisor" in prompt.lower()
        assert "professional" in prompt.lower()


class TestRAGPipelineIntegration:
    """Integration tests for RAG Pipeline with external dependencies"""
    
    @pytest.mark.asyncio
    async def test_embedding_model_loading(self):
        """Test that embedding model can be loaded (requires actual model)"""
        try:
            pipeline = RAGPipeline(
                ollama_base_url="http://localhost:11434",
                ollama_model="llama3",
                ingestion_service_url="http://localhost:8080",
                embedding_model_name="sentence-transformers/all-MiniLM-L6-v2"
            )
            
            # Test embedding generation
            result = await pipeline.generate_embeddings("Test text")
            assert result is not None
            assert len(result) > 0
            
        except Exception as e:
            pytest.skip(f"Embedding model not available: {e}")
    
    @pytest.mark.asyncio
    async def test_real_service_integration(self):
        """Test integration with real services (if available)"""
        try:
            pipeline = RAGPipeline(
                ollama_base_url="http://localhost:11434",
                ollama_model="llama3",
                ingestion_service_url="http://localhost:8080",
                embedding_model_name="sentence-transformers/all-MiniLM-L6-v2"
            )
            
            # This test will only pass if services are actually running
            result = await pipeline.generate_draft(
                subject="Test Integration",
                sender="<EMAIL>",
                content="This is an integration test."
            )
            
            assert "draft" in result
            assert "metadata" in result
            
        except Exception as e:
            pytest.skip(f"Real services not available: {e}")


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
